import { call, put, takeEvery } from "redux-saga/effects";
import { actions } from "./affiliate.slice";
import { actions as aiscriptActions } from "@/features/aiscript/aiscript.slice";
import { IPagination, ISort } from "@/interfaces";
import { PayloadAction } from "@reduxjs/toolkit";
import qs from "qs";
import { handleApiError } from "@/utils/error-handler";
import { generateAntiCacheUUID } from "@/utils/anti-cache";

function* handleFetch(
  action: PayloadAction<{
    pagination: IPagination;
    sort?: ISort[];
    filters?: Record<string, any>;
  }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoadingAffiliate(true));
    // Clear any previous errors when starting a new fetch
    yield put(actions.setError(null));
    const { pagination, sort, filters } = action.payload;

    console.log("filters affiliate:", filters);

    // check if value by key in filters is falsy, if so, remove it
    filters &&
      Object.keys(filters).forEach(
        (key) => !filters[key] && delete filters[key]
      );

    const buildFilters = {
      ...(filters || {}),
    };

    const DEFAULT_PAGE_SIZE = 10;
    const DEFAULT_SORT = ["monthly_traffic:desc"];

    const mapSortField = (s: ISort): string => {
      if (s.field === "commission_title") return `monthly_traffic:${s.order}`;
      if (s.field === "pricing") return `avg_price:${s.order}`;
      return `${s.field}:${s.order}`;
    };

    const populateConfig = {
      payment_methods: { populate: "image", fields: ["id", "name"] },
      commission: { fields: ["id", "max_percentage", "title"] },
      image: { fields: ["id", "url"] },
      industry: { fields: ["id", "name", "slug"] },
      categories: { fields: ["id", "name", "slug"] },
    };

    const query = qs.stringify(
      {
        filters: buildFilters,
        pagination: {
          page: pagination.page,
          pageSize: pagination.pageSize || DEFAULT_PAGE_SIZE,
        },
        sort: sort?.map(mapSortField) || DEFAULT_SORT,
        populate: populateConfig,
      },
      { encodeValuesOnly: true }
    );
    console.time("Fetching affiliates saga...");

    // Simplified - no manual token handling
    const response: any = yield call(fetch, `/api/affiliates?${query}`);

    console.timeEnd("Fetching affiliates saga...");
    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        return; // Error was handled
      }

      const errorMsg = `Request failed with status ${response.status}`;
      yield put(actions.setError(errorMsg));
      return;
    }
    const { data, meta } = yield response.json();
    if (!data || !meta) {
      const errorMsg = "Invalid response structure";
      yield put(actions.setError(errorMsg));
      return;
    }

    // Clear any errors on successful data fetch
    yield put(actions.setError(null));
    yield put(actions.setAffiliates(data));
    yield put(actions.setAffiliatePagination(meta.pagination));
  } catch (error: any) {
    yield put(actions.setError("Failed to fetch affiliates"));
  } finally {
    yield put(actions.setLoadingAffiliate(false));
  }
}

function* fetchDetail(
  action: PayloadAction<{ id: string }>
): Generator<any, void, any> {
  try {
    yield put(actions.setLoadingAffiliate(true));
    // Clear any previous errors when starting a new fetch
    yield put(actions.setError(null));
    const { id } = action.payload;
    const response = yield call(fetch, `/api/affiliates/${id}`);

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        return; // Error was handled
      }

      const errorMsg = `Failed to fetch affiliate details: ${response.status}`;
      yield put(actions.setError(errorMsg));
      return;
    }

    const data = yield response.json();
    console.log("LOG-data", data);
    // Clear any errors on successful data fetch
    yield put(actions.setError(null));
    yield put(actions.setCurrentAffiliate(data));
  } catch (error: any) {
    yield put(actions.setError("Failed to fetch affiliate details"));
  } finally {
    yield put(actions.setLoadingAffiliate(false));
  }
}

function* fetchAffiliateUrl(
  action: PayloadAction<{ id: string; shouldOpen?: boolean }>
): Generator<any, void, any> {
  try {
    // The ID is already set in the state by the reducer
    const { id, shouldOpen = false } = action.payload;

    const anti_cache = generateAntiCacheUUID();
    // Make the API call
    const response = yield call(
      fetch,
      `/api/affiliates/${id}/url?anti_cache=${anti_cache}`
    );

    if (!response.ok) {
      // Use the centralized error handler
      if (yield call(handleApiError, response)) {
        return; // Error was handled
      }

      const errorMsg = `Failed to fetch affiliate URL: ${response.status}`;
      yield put(actions.setError(errorMsg));
      yield put(actions.setLoadingAffiliateUrl(false)); // Reset loading
      return;
    }

    const data = yield response.json();

    // Only set the URL in state if shouldOpen is true - let the component handle opening and clearing
    if (shouldOpen) {
      yield put(actions.setAffiliateUrl(data.url));
    }

    // Always cache the URL for the specific affiliate ID, but don't set currentUrl unless shouldOpen is true
    yield put(actions.fetchAffiliateUrlFulfilled({ url: data.url, id }));
  } catch (error: any) {
    console.error("Error fetching affiliate URL:", error);
    yield put(
      actions.setError(error.message || "Failed to fetch affiliate URL")
    );
    yield put(actions.setLoadingAffiliateUrl(false)); // Reset loading on error
  }
}

function* fetchAffiliateSummary(
  action: PayloadAction<{ id: string }>
): Generator<any, void, any> {
  try {
    const { id } = action.payload;

    // Make the API call with conditional headers
    const response = yield call(fetch, `/api/affiliates/${id}/summary`);

    if (!response.ok) {
      // Use the centralized error handler
      yield put(actions.setLoadingAffiliateSummary(false));
      if (yield call(handleApiError, response)) {
        return; // Error was handled
      }

      const errorMsg = `Failed to fetch affiliate summary: ${response.status}`;
      yield put(actions.setError(errorMsg));
      return;
    }

    const data = yield response.json();

    // Integrate with AIScript similar to handleAISnippetClick in PostCard
    if (data.summary) {
      // Set the summary in state
      yield put(actions.setAffiliateSummary(data.summary));
      // Clear previous messages
      yield put(aiscriptActions.clearMessages());

      // Clean up extra newlines and whitespace from summary
      const cleanedSummary = data.summary
        .replace(/\\n/g, '\n')  // Convert literal \n to actual newlines
        .replace(/\n{3,}/g, '\n\n')  // Replace 3+ consecutive newlines with just 2
        .trim();  // Remove leading/trailing whitespace

      // Format the summary with a label
      const formattedSummary = `${cleanedSummary}`;

      const quickReplies = data.suggestions || [];

      // Add the summary to AIScript as an AI message
      yield put(
        aiscriptActions.setMessage({
          type: "ai",
          content: formattedSummary,
          copyable: true,
          quickReplies: quickReplies.map((suggestion: any) => ({
            label: suggestion.title,
            content: suggestion.content,
            promptId: suggestion.id,
          })),
        })
      );

      // Open the AIScript UI
      yield put(aiscriptActions.openAIScript());

      // set the session_id to the AIScript state
      if (data.session_id) {
        yield put(aiscriptActions.setSessionId(data.session_id));
      }
    }
  } catch (error: any) {
    console.error("Error fetching affiliate summary:", error);
    yield put(
      actions.setError(error.message || "Failed to fetch affiliate summary")
    );
    yield put(actions.setLoadingAffiliateSummary(false));
  }
}

export default function* affiliateSaga() {
  yield takeEvery(actions.fetch.type, handleFetch);
  yield takeEvery(actions.fetchDetail.type, fetchDetail);
  yield takeEvery(actions.fetchAffiliateUrl.type, fetchAffiliateUrl);
  yield takeEvery(actions.fetchAffiliateSummary.type, fetchAffiliateSummary);
}
