import { call, put, takeEvery, select, all } from 'redux-saga/effects';
import { PayloadAction } from '@reduxjs/toolkit';
import { actions, UserPromptAction, selectSessionId } from './aiscript.slice';
import axios from 'axios';
import { handleApiError, redirectToAuth } from '@/utils/error-handler';
import { actions as authActions } from '@/features/auth/auth.slice';

// Set a higher timeout for API requests (30 seconds instead of default 10)
const API_TIMEOUT = 30000;

// Helper function to handle axios errors
function* handleAxiosError(error: any): Generator<any, boolean, any> {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    return yield call(handleApiError, new Response(null, {
      status: error.response.status,
      statusText: error.response.statusText
    }));
  }
  return false;
}

// Specialized error handler for user-prompts API calls
function* handleUserPromptsError(error: any): Generator<any, boolean, any> {
  if (error.response?.status === 401) {
    console.log('🔐 [USER-PROMPTS] 401 error detected, clearing auth and redirecting to login');

    // Clear auth data
    yield put(authActions.logout());

    // Redirect to authentication page with current path as redirect parameter
    redirectToAuth();

    return true;
  }

  // For other errors, use the general handler
  return yield call(handleAxiosError, error);
}

function* handleSendMessage(
  action: PayloadAction<string>
): Generator<any, void, any> {
  try {
    // Set loading state
    yield put(actions.setLoading(true));
    
    // Get user message from action payload
    const message = action.payload;
    
    // Get sessionId from state
    const sessionId = yield select(selectSessionId);
    console.log("LOG-sessionId 3", sessionId);
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.setError('Authentication required'));
      return;
    }
    
    // Call the Next.js API route with increased timeout
    const response = yield call(axios.post, '/api/aiscript', 
      { 
        message,
        sessionId,  // Include sessionId in the request if available
        // No promptId for regular messages
      },
      { 
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT // Increase timeout to 30 seconds
      }
    );
    console.log('LOG-response', response);
    // Handle response
    if (response && response.data) {
      // Add AI response to messages with copyable flag
      yield put(actions.setMessage({ 
        type: 'ai', 
        content: response.data.message,
        copyable: true  // Add copyable flag to make AI responses copyable
      }));
      
      // Add any quick replies if provided
      if (response.data.suggestions && response.data.suggestions.length > 0) {}

      // Put the session_id to the AIScript state
      if (response.data.session_id)
        yield put(actions.setSessionId(response.data.session_id));
    } else {
      // Handle unexpected response format
      yield put(actions.setError("Received an invalid response format from the server"));
    }
  } catch (error: any) {
    // Handle specific error cases
    if (!(yield call(handleAxiosError, error))) {
      yield put(actions.setError("Failed to send message"));
    }
  } finally {
    // Always reset loading state
    yield put(actions.setLoading(false));
  }
}

// Handler for user prompts
function* handleSendUserPrompt(
  action: PayloadAction<UserPromptAction>
): Generator<any, void, any> {
  try {
    // Set loading state
    yield put(actions.setLoading(true));
    
    // Get data from the action payload
    const { content, promptId } = action.payload;
    
    // Get sessionId from state
    const sessionId = yield select(selectSessionId);

    console.log('LOG-sessionId 1', sessionId);
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.setError('Authentication required'));
      return;
    }
    
    // Call the Next.js API route with the full prompt content and promptId
    const response = yield call(axios.post, '/api/aiscript', 
      { 
        message: content,
        promptId,  // Include the promptId if available
        sessionId, // Include sessionId in the request if available
      },
      { 
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT // Increase timeout to 30 seconds
      }
    );
    
    console.log("AIScript response:", response);
    // Handle response
    if (response && response.data) {
      // Clean up extra newlines and whitespace from AI response
      const cleanedMessage = response.data.message
        .replace(/\n\s*\n\s*\n/g, '\n\n')  // Replace 3+ consecutive newlines (with possible whitespace) with just 2
        .replace(/\n\s*\n/g, '\n\n')  // Ensure double newlines are clean
        .replace(/\n{3,}/g, '\n\n')  // Replace any remaining 3+ consecutive newlines with just 2
        .trim();  // Remove leading/trailing whitespace

      yield put(actions.setMessage({
        type: 'ai',
        copyable: true,
        content: cleanedMessage
      }));
      
      // Update sessionId if it's in the response
      if (response.data.session_id) {
        yield put(actions.setSessionId(response.data.session_id));
      }
    } else {
      yield put(actions.setError("Received an invalid response format from the server"));
    }
  } catch (error: any) {
    // Handle specific error cases
    if (!(yield call(handleAxiosError, error))) {
      yield put(actions.setError("Failed to process prompt"));
    }
  } finally {
    yield put(actions.setLoading(false));
  }
}

function* handleEndSession(): Generator<any, void, any> {
  try {
    // Get sessionId from state
    const sessionId = yield select(selectSessionId);

    console.log('LOG-sessionId 2', sessionId);
    
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      console.log("No authentication token available");
      return;
    }
    
    // Call the endpoint for ending sessions
    yield call(axios.post, '/api/aiscript/end', 
      { 
        sessionId // Include sessionId in the request if available
      },
      { 
        headers: { 
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: API_TIMEOUT // Increase timeout to 30 seconds
      }
    );

    // clear the sessionId from the state
    yield put(actions.setSessionId(null));
    
    console.log("AIScript session ended successfully");
  } catch (error: any) {
    console.error("Error ending AIScript session:", error);
  }
}

function* handleFetchPrompts(): Generator<any, void, any> {
  try {
    // Get auth token from localStorage
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      console.error('🔐 [PROMPTS] Authentication token not found');
      yield put(actions.setPromptsError('Authentication required'));
      return;
    }

    console.log('📡 [PROMPTS] Fetching prompts from /api/prompts...');

    // Call the prompts API endpoint
    const response = yield call(axios.get, '/api/prompts', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT // Increase timeout to 30 seconds
    });

    console.log('📥 [PROMPTS] Raw response received:', {
      status: response?.status,
      statusText: response?.statusText,
      headers: response?.headers,
      data: response?.data,
      dataType: typeof response?.data,
      dataKeys: response?.data ? Object.keys(response.data) : 'No data object'
    });

    // Check if the response contains data
    if (response?.data?.data) {
      console.log('✅ [PROMPTS] Valid response format detected, setting prompts:', response.data.data);
      yield put(actions.setPrompts(response.data.data));
    } else {
      console.error('❌ [PROMPTS] Invalid response format detected:', {
        hasResponse: !!response,
        hasData: !!response?.data,
        hasDataData: !!response?.data?.data,
        actualStructure: response?.data,
        expectedStructure: 'Expected: { data: Prompt[] }'
      });
      yield put(actions.setPromptsError('Invalid response format'));
    }
  } catch (error: any) {
    // Handle specific error cases
    if (!(yield call(handleAxiosError, error))) {
      yield put(actions.setPromptsError("Failed to fetch prompt templates"));
    }
  }
}

function* handleOpenAIScript(): Generator<any, void, any> {
  // When opening the AIScript, fetch user prompts and recent prompts
  try {
    yield put(actions.fetchUserPrompts());
  } catch (error) {
    console.error('Error fetching user prompts on open:', error);
  }
}

// Helper function to fetch tags and recent prompts
function* handleFetchTagsAndRecent(): Generator<any, void, any> {
  try {
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      console.warn('🔐 [TAGS-RECENT] Authentication token not found');
      return;
    }

    console.log('📡 [TAGS-RECENT] Fetching tags and recent prompts...');

    const [tagsResponse, recentResponse] = yield all([
      call(axios.get, '/api/user-prompts/tags', {
        headers: { 'Authorization': `Bearer ${token}` },
        timeout: API_TIMEOUT
      }),
      call(axios.get, '/api/user-prompts/recent', {
        headers: { 'Authorization': `Bearer ${token}` },
        timeout: API_TIMEOUT
      })
    ]);

    console.log('📥 [TAGS-RECENT] Tags response:', {
      status: tagsResponse?.status,
      data: tagsResponse?.data,
      hasDataData: !!tagsResponse?.data?.data,
      isArray: Array.isArray(tagsResponse?.data)
    });

    console.log('📥 [TAGS-RECENT] Recent response:', {
      status: recentResponse?.status,
      data: recentResponse?.data,
      hasDataData: !!recentResponse?.data?.data,
      isArray: Array.isArray(recentResponse?.data)
    });

    // Handle tags response - support both formats
    let tags = null;
    if (tagsResponse?.data?.data !== undefined) {
      tags = tagsResponse.data.data;
      console.log('✅ [TAGS-RECENT] Standard tags format detected:', tags);
    } else if (Array.isArray(tagsResponse?.data)) {
      tags = tagsResponse.data;
      console.log('✅ [TAGS-RECENT] Direct array tags format detected:', tags);
    }

    if (tags !== null) {
      yield put(actions.setAvailableTags(tags));
    } else {
      console.warn('⚠️ [TAGS-RECENT] Invalid tags response format:', tagsResponse?.data);
    }

    // Handle recent prompts response - support both formats
    let recentPrompts = null;
    if (recentResponse?.data?.data !== undefined) {
      recentPrompts = recentResponse.data.data;
      console.log('✅ [TAGS-RECENT] Standard recent prompts format detected:', recentPrompts);
    } else if (Array.isArray(recentResponse?.data)) {
      recentPrompts = recentResponse.data;
      console.log('✅ [TAGS-RECENT] Direct array recent prompts format detected:', recentPrompts);
    }

    if (recentPrompts !== null) {
      yield put(actions.setRecentPrompts(recentPrompts));
    } else {
      console.warn('⚠️ [TAGS-RECENT] Invalid recent prompts response format:', recentResponse?.data);
    }
  } catch (error: any) {
    // Check for 401 errors specifically for tags and recent prompts
    if (error.response?.status === 401) {
      console.log('🔐 [TAGS-RECENT] 401 error detected, clearing auth and redirecting to login');

      // Clear auth data
      yield put(authActions.logout());

      // Redirect to authentication page
      redirectToAuth();

      return;
    }

    // Silently fail for other errors - tags and recent prompts are not critical
    console.warn('💥 [TAGS-RECENT] Failed to fetch tags and recent prompts:', error);
  }
}

// User prompt management saga handlers
function* handleFetchUserPrompts(action?: any): Generator<any, void, any> {
  try {
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      console.error('🔐 [USER-PROMPTS] Authentication token not found');
      yield put(actions.setUserPromptsError('Authentication required'));
      return;
    }

    // Get search parameters from action payload if provided
    const params = action?.payload || {};
    const queryParams = new URLSearchParams();

    if (params.search) queryParams.append('search', params.search);
    if (params.tags && params.tags.length > 0) queryParams.append('tags', JSON.stringify(params.tags));
    if (params.isFavorite !== undefined) queryParams.append('isFavorite', params.isFavorite.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.start) queryParams.append('start', params.start.toString());
    if (params.sortBy) queryParams.append('sortBy', params.sortBy);
    if (params.sortOrder) queryParams.append('sortOrder', params.sortOrder);

    const url = `/api/user-prompts${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

    console.log('📡 [USER-PROMPTS] Fetching user prompts from:', url, 'with params:', params);

    const response = yield call(axios.get, url, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    });

    console.log('📥 [USER-PROMPTS] Raw response received:', {
      status: response?.status,
      statusText: response?.statusText,
      headers: response?.headers,
      data: response?.data,
      dataType: typeof response?.data,
      dataKeys: response?.data ? Object.keys(response.data) : 'No data object',
      isArray: Array.isArray(response?.data)
    });

    // Handle both formats: { data: UserPrompt[] } and UserPrompt[] directly
    let userPrompts = null;
    if (response?.data?.data !== undefined) {
      // Standard format: { data: UserPrompt[] } - including empty arrays
      userPrompts = response.data.data;
      console.log('✅ [USER-PROMPTS] Standard format detected, setting user prompts:', userPrompts);
    } else if (Array.isArray(response?.data)) {
      // Direct array format: UserPrompt[]
      userPrompts = response.data;
      console.log('✅ [USER-PROMPTS] Direct array format detected, setting user prompts:', userPrompts);
    }

    if (userPrompts !== null) {
      yield put(actions.setUserPrompts(userPrompts));

      // Also fetch tags and recent prompts if this is the initial load (no search params)
      if (!params.search && !params.tags && params.isFavorite === undefined) {
        yield call(handleFetchTagsAndRecent);
      }
    } else {
      console.error('❌ [USER-PROMPTS] Invalid response format detected:', {
        hasResponse: !!response,
        hasData: !!response?.data,
        hasDataData: !!response?.data?.data,
        isDataArray: Array.isArray(response?.data),
        actualStructure: response?.data,
        expectedStructure: 'Expected: { data: UserPrompt[] } or UserPrompt[]'
      });
      yield put(actions.setUserPromptsError('Invalid response format'));
    }
  } catch (error: any) {
    console.error('💥 [USER-PROMPTS] Error fetching user prompts:', error);
    if (!(yield call(handleUserPromptsError, error))) {
      yield put(actions.setUserPromptsError('Failed to fetch user prompts'));
    }
  }
}

function* handleCreateUserPrompt(action: PayloadAction<any>): Generator<any, void, any> {
  try {
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      console.error('🔐 [CREATE-PROMPT] Authentication token not found');
      yield put(actions.setUserPromptsError('Authentication required'));
      return;
    }

    console.log('📡 [CREATE-PROMPT] Creating user prompt with payload:', action.payload);

    const response = yield call(axios.post, '/api/user-prompts', action.payload, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    });

    console.log('📥 [CREATE-PROMPT] Raw response received:', {
      status: response?.status,
      statusText: response?.statusText,
      headers: response?.headers,
      data: response?.data,
      dataType: typeof response?.data,
      dataKeys: response?.data ? Object.keys(response.data) : 'No data object'
    });

    // Handle both formats: { data: UserPrompt } and UserPrompt directly
    let createdPrompt = null;
    if (response?.data?.data !== undefined) {
      // Standard format: { data: UserPrompt }
      createdPrompt = response.data.data;
      console.log('✅ [CREATE-PROMPT] Standard format detected, adding user prompt:', createdPrompt);
    } else if (response?.data && typeof response.data === 'object' && !Array.isArray(response.data)) {
      // Direct object format: UserPrompt
      createdPrompt = response.data;
      console.log('✅ [CREATE-PROMPT] Direct object format detected, adding user prompt:', createdPrompt);
    }

    if (createdPrompt !== null) {
      yield put(actions.addUserPrompt(createdPrompt));
      yield put(actions.closeSavePromptModal());
    } else {
      console.error('❌ [CREATE-PROMPT] Invalid response format detected:', {
        hasResponse: !!response,
        hasData: !!response?.data,
        hasDataData: !!response?.data?.data,
        actualStructure: response?.data,
        expectedStructure: 'Expected: { data: UserPrompt } or UserPrompt'
      });
      yield put(actions.setUserPromptsError('Invalid response format from create prompt'));
    }
  } catch (error: any) {
    console.error('💥 [CREATE-PROMPT] Error creating user prompt:', error);
    if (!(yield call(handleUserPromptsError, error))) {
      yield put(actions.setUserPromptsError('Failed to create prompt'));
    }
  }
}

function* handleUpdateUserPrompt(action: PayloadAction<{id: string, data: any}>): Generator<any, void, any> {
  try {
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.setUserPromptsError('Authentication required'));
      yield put(actions.setUserPromptsLoading(false));
      return;
    }

    const { id, data } = action.payload;
    const response = yield call(axios.put, `/api/user-prompts/${id}`, data, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    });

    if (response?.data?.data) {
      // Update the prompt in local state and refresh the list
      yield put(actions.updateUserPrompt(response.data.data));
      yield put(actions.setUserPromptsLoading(false));

      // Refresh the prompt list to ensure consistency
      yield put(actions.fetchUserPrompts());
    }
  } catch (error: any) {
    console.error('💥 [UPDATE-PROMPT] Error updating user prompt:', error);
    yield put(actions.setUserPromptsLoading(false));
    if (!(yield call(handleUserPromptsError, error))) {
      yield put(actions.setUserPromptsError('Failed to update prompt'));
    }
  }
}

function* handleDeleteUserPrompt(action: PayloadAction<string>): Generator<any, void, any> {
  try {
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.setUserPromptsError('Authentication required'));
      yield put(actions.setUserPromptsLoading(false));
      return;
    }

    yield call(axios.delete, `/api/user-prompts/${action.payload}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      timeout: API_TIMEOUT
    });

    // Remove the prompt from local state and refresh the list
    yield put(actions.removeUserPrompt(action.payload));
    yield put(actions.setUserPromptsLoading(false));

    // Refresh the prompt list to ensure consistency
    yield put(actions.fetchUserPrompts());
  } catch (error: any) {
    console.error('💥 [DELETE-PROMPT] Error deleting user prompt:', error);
    yield put(actions.setUserPromptsLoading(false));
    if (!(yield call(handleUserPromptsError, error))) {
      yield put(actions.setUserPromptsError('Failed to delete prompt'));
    }
  }
}

function* handleToggleFavorite(action: PayloadAction<string>): Generator<any, void, any> {
  try {
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.setUserPromptsError('Authentication required'));
      return;
    }

    const response = yield call(axios.post, `/api/user-prompts/${action.payload}/favorite`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    });

    if (response?.data?.data) {
      yield put(actions.updateUserPrompt(response.data.data));
    }
  } catch (error: any) {
    console.error('💥 [TOGGLE-FAVORITE] Error toggling favorite:', error);
    if (!(yield call(handleUserPromptsError, error))) {
      yield put(actions.setUserPromptsError('Failed to toggle favorite'));
    }
  }
}

function* handleIncrementUsage(action: PayloadAction<string>): Generator<any, void, any> {
  try {
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    if (!token) {
      yield put(actions.setUserPromptsError('Authentication required'));
      return;
    }

    const response = yield call(axios.post, `/api/user-prompts/${action.payload}/use`, {}, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      timeout: API_TIMEOUT
    });

    if (response?.data?.data) {
      yield put(actions.updateUserPrompt(response.data.data));
    }
  } catch (error: any) {
    console.error('💥 [INCREMENT-USAGE] Error incrementing usage:', error);
    if (!(yield call(handleUserPromptsError, error))) {
      yield put(actions.setUserPromptsError('Failed to update usage count'));
    }
  }
}



export default function* aiscriptSaga() {
  yield takeEvery(actions.sendMessage.type, handleSendMessage);
  yield takeEvery(actions.sendUserPrompt.type, handleSendUserPrompt);
  yield takeEvery(actions.endSession.type, handleEndSession);
  yield takeEvery(actions.fetchPrompts.type, handleFetchPrompts);
  yield takeEvery(actions.openAIScript.type, handleOpenAIScript);

  // User prompt management sagas
  yield takeEvery(actions.fetchUserPrompts.type, handleFetchUserPrompts);
  yield takeEvery(actions.createUserPrompt.type, handleCreateUserPrompt);
  yield takeEvery(actions.updateUserPromptRequest.type, handleUpdateUserPrompt);
  yield takeEvery(actions.deleteUserPrompt.type, handleDeleteUserPrompt);
  yield takeEvery(actions.deleteUserPromptRequest.type, handleDeleteUserPrompt);
  yield takeEvery(actions.toggleUserPromptFavorite.type, handleToggleFavorite);
  yield takeEvery(actions.incrementPromptUsage.type, handleIncrementUsage);

}
